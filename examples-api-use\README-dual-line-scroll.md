# Dual-Line Scrolling Text Display (C++ Version)

A C++ program that demonstrates scrolling text on exactly 2 lines using the rpi-rgb-led-matrix library. Optimized for 32x160 LED matrix displays (5 panels of 32x32). Based on the scrolling-text-example.cc reference with enhanced dual-line functionality.

## 🎯 Features

### **Core Functionality:**
- ✅ **Dual-line display**: Shows scrolling text on exactly 2 lines simultaneously
- ✅ **32x160 display optimized**: Perfect for 5 panels of 32x32 LED matrices
- ✅ **8x13 font support**: Uses 8x13.bdf font specifically (with 7x13.bdf fallback)
- ✅ **Smooth scrolling**: Pixel-perfect character-by-character scrolling animation
- ✅ **Smart text handling**: Automatically centers short text, scrolls long text
- ✅ **Customizable content**: Independent text content for each line
- ✅ **Hardware-specific defaults**: Optimized for row-addr-type=0, multiplexing=1, gpio-slowdown=2, chain=5

### **Technical Features:**
- ✅ **Proper LED matrix initialization**: Complete matrix configuration
- ✅ **Optimized rendering**: Only draws visible characters for performance
- ✅ **Frame rate control**: Configurable FPS for smooth animation
- ✅ **Clean exit handling**: Proper cleanup on CTRL-C
- ✅ **Memory efficient**: Pre-calculated character widths for smooth scrolling

## 🚀 Build and Run

### **1. Build:**
```bash
cd examples-api-use
chmod +x build-dual-line-scroll.sh
./build-dual-line-scroll.sh
```

### **2. Basic Usage:**
```bash
# Default demo with sample text
sudo ./dual-line-scroll

# Custom text for both lines
sudo ./dual-line-scroll -t1 "Welcome to LED Matrix" -t2 "Dual line scrolling demo"
```

### **3. Advanced Usage:**
```bash
# Custom colors and speed
sudo ./dual-line-scroll -c 255,0,0 -s 2 -t1 "Red text" -t2 "Faster scrolling"

# With LED matrix configuration (32x160 display)
sudo ./dual-line-scroll --led-row-addr-type=0 --led-multiplexing=1 --led-slowdown-gpio=2 --led-chain=5 \
  -t1 "Line 1 text" -t2 "Line 2 text"

# Using different font
sudo ./dual-line-scroll -f ../fonts/6x13.bdf -t1 "Custom font" -t2 "Different size"
```

## 🔧 Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `-t1 <text>` | Text for line 1 | "Line 1: Scrolling text demo" |
| `-t2 <text>` | Text for line 2 | "Line 2: Another scrolling message" |
| `-s <speed>` | Scroll speed (pixels per frame) | 1 |
| `-f <font>` | BDF font file path | ../fonts/7x13.bdf |
| `-c <r,g,b>` | Text color (RGB values) | 0,255,0 (green) |
| `-fps <fps>` | Target frame rate | 15 |
| `--led-*` | Standard LED matrix options | Various defaults |

## 📊 Display Layout

```
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                                                                                                     │
│                                                    Line 1: Scrolling text demo                                                                                     │  ← Line 1 (Y=12)
│                                                                                                                                                                     │
│                                                    Line 2: Another scrolling message                                                                              │  ← Line 2 (Y=26)
│                                                                                                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
                                                                    32x160 pixels (5 panels of 32x32)
```

- **Display dimensions**: 32 rows × 160 columns (5 chained 32×32 panels)
- **Line positioning**: Line 1 at Y=12, Line 2 at Y=26 (optimized for 32-pixel height)
- **Text behavior**: Short text is centered, long text scrolls smoothly across full 160-pixel width
- **Font**: 8x13.bdf preferred, 7x13.bdf fallback
- **Hardware**: Optimized for row-addr-type=0, multiplexing=1, gpio-slowdown=2, chain=5

## 🎨 Customization Examples

### **Different Colors:**
```bash
# Red text
sudo ./dual-line-scroll -c 255,0,0 -t1 "Red Line 1" -t2 "Red Line 2"

# Blue text
sudo ./dual-line-scroll -c 0,0,255 -t1 "Blue Line 1" -t2 "Blue Line 2"

# Yellow text
sudo ./dual-line-scroll -c 255,255,0 -t1 "Yellow Line 1" -t2 "Yellow Line 2"
```

### **Different Speeds:**
```bash
# Slow scrolling
sudo ./dual-line-scroll -s 1 -t1 "Slow scroll" -t2 "One pixel per frame"

# Fast scrolling
sudo ./dual-line-scroll -s 3 -t1 "Fast scroll" -t2 "Three pixels per frame"
```

### **Different Fonts:**
```bash
# Smaller font
sudo ./dual-line-scroll -f ../fonts/6x10.bdf -t1 "Small font" -t2 "6x10 pixels"

# Larger font (if available)
sudo ./dual-line-scroll -f ../fonts/8x13.bdf -t1 "Large font" -t2 "8x13 pixels"
```

## 🔍 Code Structure

### **Key Components:**

1. **ScrollData Structure**: Holds text and pre-calculated character widths
2. **PrepareScrollData()**: Calculates character widths for smooth scrolling
3. **DrawScrollingText()**: Renders scrolling text with pixel-perfect positioning
4. **DrawCenteredText()**: Centers short text that fits on screen
5. **Main Loop**: Handles dual-line rendering and frame timing

### **Scrolling Algorithm:**
- Pre-calculates character widths for each character
- Uses pixel-perfect positioning for smooth animation
- Only renders visible characters for performance
- Wraps around seamlessly when text reaches the end

## 🐛 Troubleshooting

### **Font Issues:**
```bash
# Check if fonts exist
ls -la ../fonts/8x13.bdf
ls -la ../fonts/7x13.bdf

# Try different font
sudo ./dual-line-scroll -f ../fonts/6x13.bdf
```

### **Display Issues:**
```bash
# Check LED matrix configuration
sudo ./dual-line-scroll --led-show-refresh

# Try different matrix settings
sudo ./dual-line-scroll --led-rows=32 --led-cols=64
```

### **Build Issues:**
```bash
# Clean and rebuild
make -f Makefile.dual-line-scroll clean
make -f Makefile.dual-line-scroll

# Check dependencies
sudo apt-get install build-essential
```

## 📝 Technical Notes

- **Language**: C++ (with C-style implementation for clarity)
- **Dependencies**: rpi-rgb-led-matrix library
- **Performance**: Optimized for Raspberry Pi with smooth 15+ FPS
- **Memory**: Efficient character width caching
- **Threading**: Single-threaded with frame rate control

## 🎯 Use Cases

- **Digital signage**: Dual-line information display
- **Status displays**: System information on two lines
- **Advertising**: Rotating messages on separate lines
- **Notifications**: Real-time updates with dual content
- **Demonstrations**: LED matrix scrolling text showcase
