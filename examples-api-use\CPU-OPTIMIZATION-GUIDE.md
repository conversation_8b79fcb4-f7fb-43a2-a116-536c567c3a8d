# CPU Optimization Guide for LED Matrix Display

## 🚀 Performance Optimization Strategies

### 1. **Single Font vs Multi-Font Impact**

**Single Font Benefits:**
- ~5-10% less CPU usage
- Simpler font loading and caching
- Reduced memory footprint
- Faster character rendering

**Multi-Font Benefits:**
- Better visual distinction between sections
- More flexible design options
- Section-specific optimization

**Recommendation:** Use single font (`-f`) for CPU-constrained systems like Pi Zero.

### 2. **CPU Optimization Settings**

```bash
# Enable CPU optimizations for Pi Zero
sudo ./dual-line-scroll --cpu-opt --led-row-addr-type=0 --led-multiplexing=1 \
  --led-gpio-slowdown=2 --led-chain=5 -fps 8 -s 1

# Standard performance (Pi 3/4)
sudo ./dual-line-scroll --led-row-addr-type=0 --led-multiplexing=1 \
  --led-gpio-slowdown=2 --led-chain=5 -fps 15 -s 1
```

### 3. **Frame Rate Impact on CPU**

| FPS | CPU Usage | Smoothness | Recommended For |
|-----|-----------|------------|-----------------|
| 5   | Very Low  | Choppy     | Pi Zero (emergency) |
| 8   | Low       | Acceptable | Pi Zero (optimal) |
| 10  | Medium    | Good       | Pi Zero W |
| 15  | High      | Smooth     | Pi 3/4 (default) |
| 20+ | Very High | Very Smooth| Pi 4 (high-end) |

### 4. **Scroll Speed vs CPU Usage**

- **Speed 1**: Lowest CPU usage, smooth scrolling
- **Speed 2**: Medium CPU usage, faster scrolling
- **Speed 3+**: Higher CPU usage, may cause frame drops on Pi Zero

## 🔧 LED Matrix Parameters for 32x160 (5 panels)

### **Essential Parameters:**
```bash
--led-rows=32                # Panel height
--led-cols=32                # Single panel width
--led-chain=5                # Number of panels (32*5=160 width)
--led-row-addr-type=0        # Standard row addressing
--led-multiplexing=1         # Standard multiplexing
```

### **Performance Parameters:**
```bash
--led-gpio-slowdown=2        # GPIO timing (Pi Zero: 2-4, Pi 3/4: 1-2)
--led-pwm-bits=8             # Color depth (8=256 colors, 11=2048 colors)
--led-pwm-lsb-nanoseconds=130 # PWM timing
--led-pwm-dither-bits=0      # Dithering (0=off for performance)
```

### **Quality vs Performance Trade-offs:**

#### **Maximum Performance (Pi Zero):**
```bash
sudo ./dual-line-scroll --cpu-opt \
  --led-row-addr-type=0 --led-multiplexing=1 --led-chain=5 \
  --led-gpio-slowdown=4 --led-pwm-bits=8 --led-pwm-dither-bits=0 \
  --led-limit-refresh=60 -fps 8 -s 1 -f ../fonts/6x13.bdf
```

#### **Balanced Quality/Performance (Pi 3/4):**
```bash
sudo ./dual-line-scroll \
  --led-row-addr-type=0 --led-multiplexing=1 --led-chain=5 \
  --led-gpio-slowdown=2 --led-pwm-bits=11 --led-pwm-dither-bits=1 \
  --led-limit-refresh=120 -fps 15 -s 1
```

#### **Maximum Quality (Pi 4):**
```bash
sudo ./dual-line-scroll \
  --led-row-addr-type=0 --led-multiplexing=1 --led-chain=5 \
  --led-gpio-slowdown=1 --led-pwm-bits=11 --led-pwm-dither-bits=2 \
  --led-limit-refresh=200 -fps 20 -s 2
```

## 📊 CPU Usage Monitoring

### **Monitor CPU Usage:**
```bash
# Run in another terminal while display is running
top -p $(pgrep dual-line-scroll)

# Or use htop for better visualization
htop -p $(pgrep dual-line-scroll)
```

### **Expected CPU Usage:**
- **Pi Zero**: 40-60% (optimized), 70-90% (standard)
- **Pi Zero W**: 30-50% (optimized), 50-70% (standard)
- **Pi 3**: 15-25% (standard), 10-15% (optimized)
- **Pi 4**: 8-15% (standard), 5-10% (optimized)

## ⚡ Additional Optimizations

### **System-Level Optimizations:**
```bash
# Increase GPU memory split
sudo raspi-config # Advanced Options > Memory Split > 128

# Disable unnecessary services
sudo systemctl disable bluetooth
sudo systemctl disable wifi-powersave

# Set CPU governor to performance
echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor
```

### **Compile-Time Optimizations:**
```bash
# Build with optimizations
make CXXFLAGS="-O3 -march=native -mtune=native" -j$(nproc)
```

## 🎯 Recommended Configurations

### **Pi Zero (CPU-Constrained):**
```bash
sudo ./dual-line-scroll --cpu-opt -f ../fonts/6x13.bdf -fps 8 -s 1 -ls 3 \
  --led-gpio-slowdown=4 --led-pwm-bits=8 --led-chain=5
```

### **Pi 3/4 (Standard):**
```bash
sudo ./dual-line-scroll -flr ../fonts/6x13.bdf -fm ../fonts/7x14.bdf -fps 15 -s 1 \
  --led-gpio-slowdown=2 --led-pwm-bits=11 --led-chain=5
```

### **Pi 4 (High Performance):**
```bash
sudo ./dual-line-scroll -flr ../fonts/8x13.bdf -fm ../fonts/7x14.bdf -fps 20 -s 2 \
  --led-gpio-slowdown=1 --led-pwm-bits=11 --led-chain=5
```
