// -*- mode: c++; c-basic-offset: 2; indent-tabs-mode: nil; -*-
// Dual-line 3-section scrolling text display - C++ version (C-style implementation)
// Based on scrolling-text-example.cc reference and showremotetext-3part-working.cc
// Demonstrates scrolling text on exactly 2 lines with 3 sections each using 8x13 font
// Layout: Left (32px) | Middle (96px scrolling) | Right (32px)
// Optimized for 32x160 LED matrix display (32 rows, 5 panels of 32x32)
//
// This code is public domain
// (but note, that the led-matrix library this depends on is GPL v2)

#include "led-matrix.h"
#include "graphics.h"

#include <unistd.h>
#include <math.h>
#include <stdio.h>
#include <signal.h>
#include <string.h>
#include <stdlib.h>
#include <time.h>
#include <getopt.h>

using namespace rgb_matrix;

volatile bool interrupt_received = false;
static void InterruptHandler(int signo) {
  interrupt_received = true;
}

// Structure to hold scroll data for each line
struct ScrollData {
  char text[512];
  int total_width;
  int char_widths[512];
  int char_count;
  bool valid;
};

// Initialize scroll data structure
void InitScrollData(struct ScrollData* scroll_data) {
  memset(scroll_data, 0, sizeof(struct ScrollData));
  scroll_data->valid = false;
}

// Prepare scroll data - calculate character widths for smooth scrolling
void PrepareScrollData(struct ScrollData* scroll_data, const char* text, const Font* font) {
  if (!text || strlen(text) == 0) {
    scroll_data->valid = false;
    return;
  }
  
  // Copy text and add padding for smooth scrolling
  snprintf(scroll_data->text, sizeof(scroll_data->text), "%s   ", text);
  
  // Pre-calculate character widths for smooth pixel-perfect scrolling
  scroll_data->total_width = 0;
  scroll_data->char_count = strlen(scroll_data->text);
  
  for (int i = 0; i < scroll_data->char_count; i++) {
    scroll_data->char_widths[i] = font->CharacterWidth(scroll_data->text[i]);
    scroll_data->total_width += scroll_data->char_widths[i];
  }
  
  scroll_data->valid = true;
}

// Draw scrolling text with pixel-perfect character positioning
void DrawScrollingText(FrameCanvas* canvas, const Font* font, const struct ScrollData* scroll_data, 
                      int scroll_pos, int x_start, int y, int width, const Color* color) {
  if (!scroll_data->valid) return;
  
  // Calculate starting position in pixel space
  int pixel_x = -scroll_pos;
  int x_draw = x_start;
  
  // Draw only visible characters for performance
  for (int i = 0; i < scroll_data->char_count && pixel_x < width; i++) {
    int char_width = scroll_data->char_widths[i];
    
    // Only draw character if it's at least partially visible
    if (pixel_x + char_width > 0) {
      char single_char[2] = {scroll_data->text[i], '\0'};
      DrawText(canvas, *font, x_draw + pixel_x, y, *color, NULL, single_char);
    }
    pixel_x += char_width;
  }
}

// Draw centered text if it fits within the specified width
void DrawCenteredText(FrameCanvas* canvas, const Font* font, const char* text,
                     int x_start, int y, int width, const Color* color) {
  if (!text || strlen(text) == 0) return;

  // Calculate total text width
  int text_width = 0;
  for (int i = 0; text[i] != '\0'; i++) {
    text_width += font->CharacterWidth(text[i]);
  }

  // Center the text if it fits within the specified width
  if (text_width <= width) {
    int x_pos = x_start + (width - text_width) / 2;
    DrawText(canvas, *font, x_pos, y, *color, NULL, text);
  }
}

// Draw left-aligned text (clipped to width)
void DrawLeftAlignedText(FrameCanvas* canvas, const Font* font, const char* text,
                        int x_start, int y, int width, const Color* color) {
  if (!text || strlen(text) == 0) return;

  // Calculate text width to check if clipping is needed
  int text_width = 0;
  for (int i = 0; text[i] != '\0'; i++) {
    text_width += font->CharacterWidth(text[i]);
  }

  if (text_width <= width) {
    // Text fits, draw normally
    DrawText(canvas, *font, x_start, y, *color, NULL, text);
  } else {
    // Text too long, draw character by character until width limit
    int pixel_x = 0;
    for (int i = 0; text[i] != '\0' && pixel_x < width; i++) {
      int char_width = font->CharacterWidth(text[i]);
      if (pixel_x + char_width <= width) {
        char single_char[2] = {text[i], '\0'};
        DrawText(canvas, *font, x_start + pixel_x, y, *color, NULL, single_char);
        pixel_x += char_width;
      } else {
        break; // Stop if next character would exceed width
      }
    }
  }
}

// Draw right-aligned text (clipped to width)
void DrawRightAlignedText(FrameCanvas* canvas, const Font* font, const char* text,
                         int x_start, int y, int width, const Color* color) {
  if (!text || strlen(text) == 0) return;

  // Calculate total text width
  int text_width = 0;
  for (int i = 0; text[i] != '\0'; i++) {
    text_width += font->CharacterWidth(text[i]);
  }

  if (text_width <= width) {
    // Text fits, right-align it
    int x_pos = x_start + width - text_width;
    DrawText(canvas, *font, x_pos, y, *color, NULL, text);
  } else {
    // Text too long, truncate from left and right-align
    // Find how many characters fit from the right
    int fit_width = 0;
    int start_char = strlen(text);

    for (int i = strlen(text) - 1; i >= 0 && fit_width < width; i--) {
      int char_width = font->CharacterWidth(text[i]);
      if (fit_width + char_width <= width) {
        fit_width += char_width;
        start_char = i;
      } else {
        break;
      }
    }

    // Draw the fitting portion right-aligned
    if (start_char < strlen(text)) {
      const char* truncated_text = &text[start_char];
      int x_pos = x_start + width - fit_width;
      DrawText(canvas, *font, x_pos, y, *color, NULL, truncated_text);
    }
  }
}

static int usage(const char *progname) {
  fprintf(stderr, "usage: %s [options]\n", progname);
  fprintf(stderr, "Dual-line 3-section scrolling text for 32x160 LED matrix (5 panels of 32x32)\n");
  fprintf(stderr, "Layout: Left(32px) | Middle(96px scrolling) | Right(32px)\n");
  fprintf(stderr, "\nText Options:\n");
  fprintf(stderr, "\t-t1l <text>  : Line 1 Left section text (32px, left-aligned)\n");
  fprintf(stderr, "\t-t1m <text>  : Line 1 Middle section text (96px, scrolling)\n");
  fprintf(stderr, "\t-t1r <text>  : Line 1 Right section text (32px, right-aligned)\n");
  fprintf(stderr, "\t-t2l <text>  : Line 2 Left section text (32px, left-aligned)\n");
  fprintf(stderr, "\t-t2m <text>  : Line 2 Middle section text (96px, scrolling)\n");
  fprintf(stderr, "\t-t2r <text>  : Line 2 Right section text (32px, right-aligned)\n");
  fprintf(stderr, "\nDisplay Options:\n");
  fprintf(stderr, "\t-s <speed>   : Scroll speed in pixels per frame (default: 1)\n");
  fprintf(stderr, "\t-f <font>    : BDF font file (default: ../fonts/7x13.bdf - closest to 8x13)\n");
  fprintf(stderr, "\t-c <r,g,b>   : Text color (default: 0,255,0)\n");
  fprintf(stderr, "\t-fps <fps>   : Target FPS (default: 15)\n");
  fprintf(stderr, "\nDefaults optimized for: --led-row-addr-type=0 --led-multiplexing=1 --led-slowdown-gpio=2 --led-chain=5\n");
  fprintf(stderr, "\n");
  rgb_matrix::PrintMatrixFlags(stderr);
  return 1;
}

static bool parseColor(Color *c, const char *str) {
  return sscanf(str, "%hhu,%hhu,%hhu", &c->r, &c->g, &c->b) == 3;
}

int main(int argc, char *argv[]) {
  RGBMatrix::Options matrix_options;
  rgb_matrix::RuntimeOptions runtime_opt;

  // Set defaults for 32x160 LED matrix (32 rows, 160 cols = 5 panels of 32x32)
  // Using the specified hardware configuration
  matrix_options.hardware_mapping = "regular";
  matrix_options.rows = 32;
  matrix_options.cols = 32;  // Single panel width
  matrix_options.chain_length = 5;  // 5 panels chained = 160 total width
  matrix_options.parallel = 1;
  matrix_options.brightness = 80;
  matrix_options.pwm_bits = 11;
  matrix_options.pwm_lsb_nanoseconds = 130;
  matrix_options.led_rgb_sequence = "RGB";
  matrix_options.pixel_mapper_config = "";
  matrix_options.row_address_type = 0;  // As specified: --led-row-addr-type=0
  matrix_options.multiplexing = 1;      // As specified: --led-multiplexing=1
  matrix_options.scan_mode = 0;
  matrix_options.disable_hardware_pulsing = false;
  matrix_options.show_refresh_rate = false;
  matrix_options.inverse_colors = false;
  
  // Set runtime options for specified GPIO slowdown
  runtime_opt.gpio_slowdown = 2;  // As specified: --led-slowdown-gpio=2

  // Parse matrix options first
  if (!rgb_matrix::ParseOptionsFromFlags(&argc, &argv, &matrix_options, &runtime_opt)) {
    return usage(argv[0]);
  }

  // Application-specific options - 3-section layout
  const char* line1_left = "L1L";     // Line 1 Left section (32px)
  const char* line1_middle = "Line 1 Middle: Scrolling text demo";  // Line 1 Middle section (96px, scrolling)
  const char* line1_right = "L1R";    // Line 1 Right section (32px)
  const char* line2_left = "L2L";     // Line 2 Left section (32px)
  const char* line2_middle = "Line 2 Middle: Another scrolling message";  // Line 2 Middle section (96px, scrolling)
  const char* line2_right = "L2R";    // Line 2 Right section (32px)

  const char* font_file = "../fonts/7x13.bdf";  // Closest to 8x13 available
  Color text_color(0, 255, 0); // Green
  int scroll_speed = 1;
  int target_fps = 15;

  // Custom argument parsing for 6 text parameters
  for (int i = 1; i < argc; i++) {
    if (strcmp(argv[i], "-t1l") == 0 && i + 1 < argc) {
      line1_left = argv[i + 1];
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-t1m") == 0 && i + 1 < argc) {
      line1_middle = argv[i + 1];
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-t1r") == 0 && i + 1 < argc) {
      line1_right = argv[i + 1];
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-t2l") == 0 && i + 1 < argc) {
      line2_left = argv[i + 1];
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-t2m") == 0 && i + 1 < argc) {
      line2_middle = argv[i + 1];
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-t2r") == 0 && i + 1 < argc) {
      line2_right = argv[i + 1];
      i++; // Skip next argument
    }
  }

  int opt;
  while ((opt = getopt(argc, argv, "s:f:c:")) != -1) {
    switch (opt) {
      case 's': scroll_speed = atoi(optarg); break;
      case 'f': font_file = optarg; break;
      case 'c':
        if (!parseColor(&text_color, optarg)) {
          fprintf(stderr, "Invalid color spec: %s\n", optarg);
          return usage(argv[0]);
        }
        break;
      default: return usage(argv[0]);
    }
  }

  // Set up signal handler for clean exit
  signal(SIGTERM, InterruptHandler);
  signal(SIGINT, InterruptHandler);

  // Create matrix
  RGBMatrix *matrix = RGBMatrix::CreateFromOptions(matrix_options, runtime_opt);
  if (matrix == NULL) {
    fprintf(stderr, "Failed to create matrix\n");
    return 1;
  }

  // Load font - try 8x13 first, fallback to 7x13
  Font font;
  if (!font.LoadFont("../fonts/8x13.bdf")) {
    if (!font.LoadFont(font_file)) {
      fprintf(stderr, "Couldn't load font '%s' or 8x13.bdf\n", font_file);
      return 1;
    } else {
      printf("Using font: %s (8x13.bdf not found)\n", font_file);
    }
  } else {
    printf("Using 8x13.bdf font\n");
  }

  printf("Dual-line 3-section scrolling text display starting...\n");
  printf("Display: 32x160 (32 rows, 5 panels of 32x32)\n");
  printf("Layout: Left(32px) | Middle(96px scrolling) | Right(32px)\n");
  printf("Hardware config: row-addr-type=0, multiplexing=1, gpio-slowdown=2, chain=5\n");
  printf("Line 1 - Left: '%s' | Middle: '%s' | Right: '%s'\n", line1_left, line1_middle, line1_right);
  printf("Line 2 - Left: '%s' | Middle: '%s' | Right: '%s'\n", line2_left, line2_middle, line2_right);
  printf("Scroll speed: %d pixels/frame\n", scroll_speed);
  printf("Target FPS: %d\n", target_fps);
  printf("Press CTRL-C to stop.\n");

  // Display loop setup
  FrameCanvas *offscreen = matrix->CreateFrameCanvas();

  // Initialize scroll data for middle sections only
  struct ScrollData scroll_data1_middle, scroll_data2_middle;
  InitScrollData(&scroll_data1_middle);
  InitScrollData(&scroll_data2_middle);

  // Prepare scroll data for middle sections
  PrepareScrollData(&scroll_data1_middle, line1_middle, &font);
  PrepareScrollData(&scroll_data2_middle, line2_middle, &font);

  // Scroll positions for middle sections
  int scroll_pos1_middle = 0;
  int scroll_pos2_middle = 0;

  // Display area configuration for 32x160 with 3-section layout
  const int display_width = matrix->width();   // Should be 160
  const int display_height = matrix->height(); // Should be 32

  // Section layout constants
  const int LEFT_WIDTH = 32;      // Left section width
  const int RIGHT_WIDTH = 32;     // Right section width
  const int MIDDLE_WIDTH = 96;    // Middle section width (160 - 32 - 32)
  const int LEFT_X = 0;           // Left section X position
  const int MIDDLE_X = 32;        // Middle section X position
  const int RIGHT_X = 128;        // Right section X position (160 - 32)

  // Line positioning - optimized for 32 pixel height with 8x13 or 7x13 font
  const int line1_y = 12;  // First line at y=12 (good for 7x13 or 8x13 font)
  const int line2_y = 26;  // Second line at y=26 (leaves 6 pixels at bottom)

  // Frame timing
  const int frame_delay_usec = 1000000 / target_fps;

  printf("Actual display area: %dx%d pixels\n", display_width, display_height);
  printf("Section layout: Left(0-%d) | Middle(%d-%d) | Right(%d-%d)\n",
         LEFT_WIDTH-1, MIDDLE_X, MIDDLE_X+MIDDLE_WIDTH-1, RIGHT_X, RIGHT_X+RIGHT_WIDTH-1);
  printf("Line 1 Y position: %d\n", line1_y);
  printf("Line 2 Y position: %d\n", line2_y);

  // Main display loop
  while (!interrupt_received) {
    offscreen->Clear();

    // === LINE 1 SECTIONS ===

    // Line 1 - Left section (static, left-aligned)
    DrawLeftAlignedText(offscreen, &font, line1_left, LEFT_X, line1_y, LEFT_WIDTH, &text_color);

    // Line 1 - Right section (static, right-aligned)
    DrawRightAlignedText(offscreen, &font, line1_right, RIGHT_X, line1_y, RIGHT_WIDTH, &text_color);

    // Line 1 - Middle section (scrolling)
    if (scroll_data1_middle.valid) {
      if (scroll_data1_middle.total_width <= MIDDLE_WIDTH) {
        // Center the text if it fits
        DrawCenteredText(offscreen, &font, line1_middle, MIDDLE_X, line1_y, MIDDLE_WIDTH, &text_color);
      } else {
        // Scroll the text if it's too long
        DrawScrollingText(offscreen, &font, &scroll_data1_middle, scroll_pos1_middle, MIDDLE_X, line1_y, MIDDLE_WIDTH, &text_color);
        scroll_pos1_middle = (scroll_pos1_middle + scroll_speed) % scroll_data1_middle.total_width;
      }
    }

    // === LINE 2 SECTIONS ===

    // Line 2 - Left section (static, left-aligned)
    DrawLeftAlignedText(offscreen, &font, line2_left, LEFT_X, line2_y, LEFT_WIDTH, &text_color);

    // Line 2 - Right section (static, right-aligned)
    DrawRightAlignedText(offscreen, &font, line2_right, RIGHT_X, line2_y, RIGHT_WIDTH, &text_color);

    // Line 2 - Middle section (scrolling)
    if (scroll_data2_middle.valid) {
      if (scroll_data2_middle.total_width <= MIDDLE_WIDTH) {
        // Center the text if it fits
        DrawCenteredText(offscreen, &font, line2_middle, MIDDLE_X, line2_y, MIDDLE_WIDTH, &text_color);
      } else {
        // Scroll the text if it's too long
        DrawScrollingText(offscreen, &font, &scroll_data2_middle, scroll_pos2_middle, MIDDLE_X, line2_y, MIDDLE_WIDTH, &text_color);
        scroll_pos2_middle = (scroll_pos2_middle + scroll_speed) % scroll_data2_middle.total_width;
      }
    }

    // Swap buffers for smooth animation
    offscreen = matrix->SwapOnVSync(offscreen);

    // Frame rate control
    usleep(frame_delay_usec);
  }

  // Cleanup and exit
  printf("\nShutting down...\n");
  delete matrix;

  return 0;
}
