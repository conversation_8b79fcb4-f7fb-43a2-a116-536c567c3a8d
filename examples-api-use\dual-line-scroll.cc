// -*- mode: c++; c-basic-offset: 2; indent-tabs-mode: nil; -*-
// Dual-line 3-section scrolling text display - C++ version (C-style implementation)
// Based on scrolling-text-example.cc reference and showremotetext-3part-working.cc
// Demonstrates scrolling text on exactly 2 lines with 3 sections each using 8x13 font
// Layout: Left (32px) | Middle (96px scrolling) | Right (32px)
// Optimized for 32x160 LED matrix display (32 rows, 5 panels of 32x32)
//
// Version: 2.4.0 - Manual font selection per section + adjustable line spacing
// Build Date: 2025-01-18
//
// This code is public domain
// (but note, that the led-matrix library this depends on is GPL v2)

#include "led-matrix.h"
#include "graphics.h"

#include <unistd.h>
#include <math.h>
#include <stdio.h>
#include <signal.h>
#include <string.h>
#include <stdlib.h>
#include <time.h>
#include <getopt.h>


using namespace rgb_matrix;

volatile bool interrupt_received = false;
static void InterruptHandler(int signo) {
  interrupt_received = true;
}

// Structure to hold scroll data for each line
struct ScrollData {
  char text[512];
  int total_width;
  int char_widths[512];
  int char_count;
  bool valid;
};

// Initialize scroll data structure
void InitScrollData(struct ScrollData* scroll_data) {
  memset(scroll_data, 0, sizeof(struct ScrollData));
  scroll_data->valid = false;
}

// Prepare scroll data - calculate character widths for smooth scrolling
void PrepareScrollData(struct ScrollData* scroll_data, const char* text, const Font* font) {
  if (!text || strlen(text) == 0) {
    scroll_data->valid = false;
    return;
  }
  
  // Copy text and add padding for smooth scrolling
  snprintf(scroll_data->text, sizeof(scroll_data->text), "%s   ", text);
  
  // Pre-calculate character widths for smooth pixel-perfect scrolling
  scroll_data->total_width = 0;
  scroll_data->char_count = strlen(scroll_data->text);
  
  for (int i = 0; i < scroll_data->char_count; i++) {
    scroll_data->char_widths[i] = font->CharacterWidth(scroll_data->text[i]);
    scroll_data->total_width += scroll_data->char_widths[i];
  }
  
  scroll_data->valid = true;
}



// Draw scrolling text with proper pixel-level clipping to middle section boundaries
void DrawScrollingText(FrameCanvas* canvas, const Font* font, const struct ScrollData* scroll_data,
                      int scroll_pos, int x_start, int y, int width, const Color* color) {
  if (!scroll_data->valid) return;

  // Define exact clipping boundaries for the middle section
  const int clip_left = x_start;           // x=32 for middle sections
  const int clip_right = x_start + width;  // x=128 for middle sections

  // Calculate starting position for smooth pixel-by-pixel scrolling
  int x = x_start - scroll_pos;

  // Create a temporary canvas or use pixel-level masking approach
  // Since RGB matrix library doesn't have built-in text clipping, we'll implement it manually

  // Step 1: Draw characters and track which ones need clipping
  for (int i = 0; i < scroll_data->char_count; i++) {
    int char_width = scroll_data->char_widths[i];

    // Only process characters that might be visible in the clipping area
    if (x + char_width > clip_left && x < clip_right) {
      char single_char[2] = {scroll_data->text[i], '\0'};

      // Check if character needs clipping
      if (x < clip_left || x + char_width > clip_right) {
        // Character extends beyond boundaries - needs pixel-level clipping
        // We'll draw it to a temporary location and copy only the visible pixels

        // For now, implement a simpler approach: draw the character normally
        // and then clear pixels outside the boundaries
        DrawText(canvas, *font, x, y, *color, NULL, single_char);

        // Clear pixels that extend beyond the left boundary
        if (x < clip_left) {
          for (int clear_x = x; clear_x < clip_left && clear_x < x + char_width; clear_x++) {
            for (int clear_y = y - 12; clear_y <= y + 4; clear_y++) {
              if (clear_y >= 0 && clear_y < 32) {
                canvas->SetPixel(clear_x, clear_y, 0, 0, 0);
              }
            }
          }
        }

        // Clear pixels that extend beyond the right boundary
        if (x + char_width > clip_right) {
          for (int clear_x = clip_right; clear_x < x + char_width && clear_x < 160; clear_x++) {
            for (int clear_y = y - 12; clear_y <= y + 4; clear_y++) {
              if (clear_y >= 0 && clear_y < 32) {
                canvas->SetPixel(clear_x, clear_y, 0, 0, 0);
              }
            }
          }
        }
      } else {
        // Character is fully within boundaries - draw normally
        DrawText(canvas, *font, x, y, *color, NULL, single_char);
      }
    }

    // Move to next character position
    x += char_width;

    // Optimization: stop if we're way past the clipping area
    if (x > clip_right + 50) break;
  }

  // Handle wraparound for continuous scrolling
  if (x < clip_right) {
    int wrap_x = x;
    for (int i = 0; i < scroll_data->char_count && wrap_x < clip_right + 50; i++) {
      int char_width = scroll_data->char_widths[i];

      // Only process characters that might be visible in the clipping area
      if (wrap_x + char_width > clip_left && wrap_x < clip_right) {
        char single_char[2] = {scroll_data->text[i], '\0'};

        // Apply the same clipping logic as above
        if (wrap_x < clip_left || wrap_x + char_width > clip_right) {
          DrawText(canvas, *font, wrap_x, y, *color, NULL, single_char);

          // Clear pixels beyond left boundary
          if (wrap_x < clip_left) {
            for (int clear_x = wrap_x; clear_x < clip_left && clear_x < wrap_x + char_width; clear_x++) {
              for (int clear_y = y - 12; clear_y <= y + 4; clear_y++) {
                if (clear_y >= 0 && clear_y < 32) {
                  canvas->SetPixel(clear_x, clear_y, 0, 0, 0);
                }
              }
            }
          }

          // Clear pixels beyond right boundary
          if (wrap_x + char_width > clip_right) {
            for (int clear_x = clip_right; clear_x < wrap_x + char_width && clear_x < 160; clear_x++) {
              for (int clear_y = y - 12; clear_y <= y + 4; clear_y++) {
                if (clear_y >= 0 && clear_y < 32) {
                  canvas->SetPixel(clear_x, clear_y, 0, 0, 0);
                }
              }
            }
          }
        } else {
          DrawText(canvas, *font, wrap_x, y, *color, NULL, single_char);
        }
      }

      wrap_x += char_width;
    }
  }
}

// Draw centered text if it fits within the specified width
void DrawCenteredText(FrameCanvas* canvas, const Font* font, const char* text,
                     int x_start, int y, int width, const Color* color) {
  if (!text || strlen(text) == 0) return;

  // Calculate total text width
  int text_width = 0;
  for (int i = 0; text[i] != '\0'; i++) {
    text_width += font->CharacterWidth(text[i]);
  }

  // Center the text if it fits within the specified width
  if (text_width <= width) {
    int x_pos = x_start + (width - text_width) / 2;
    DrawText(canvas, *font, x_pos, y, *color, NULL, text);
  }
}

// Draw left-aligned text (clipped to width)
void DrawLeftAlignedText(FrameCanvas* canvas, const Font* font, const char* text,
                        int x_start, int y, int width, const Color* color) {
  if (!text || strlen(text) == 0) return;

  // Calculate text width to check if clipping is needed
  int text_width = 0;
  for (int i = 0; text[i] != '\0'; i++) {
    text_width += font->CharacterWidth(text[i]);
  }

  if (text_width <= width) {
    // Text fits, draw normally
    DrawText(canvas, *font, x_start, y, *color, NULL, text);
  } else {
    // Text too long, draw character by character until width limit
    int pixel_x = 0;
    for (int i = 0; text[i] != '\0' && pixel_x < width; i++) {
      int char_width = font->CharacterWidth(text[i]);
      if (pixel_x + char_width <= width) {
        char single_char[2] = {text[i], '\0'};
        DrawText(canvas, *font, x_start + pixel_x, y, *color, NULL, single_char);
        pixel_x += char_width;
      } else {
        break; // Stop if next character would exceed width
      }
    }
  }
}

// Draw right-aligned text (clipped to width)
void DrawRightAlignedText(FrameCanvas* canvas, const Font* font, const char* text,
                         int x_start, int y, int width, const Color* color) {
  if (!text || strlen(text) == 0) return;

  // Calculate total text width
  int text_width = 0;
  for (int i = 0; text[i] != '\0'; i++) {
    text_width += font->CharacterWidth(text[i]);
  }

  if (text_width <= width) {
    // Text fits, right-align it
    int x_pos = x_start + width - text_width;
    DrawText(canvas, *font, x_pos, y, *color, NULL, text);
  } else {
    // Text too long, truncate from left and right-align
    // Find how many characters fit from the right
    int fit_width = 0;
    int start_char = strlen(text);

    for (int i = strlen(text) - 1; i >= 0 && fit_width < width; i--) {
      int char_width = font->CharacterWidth(text[i]);
      if (fit_width + char_width <= width) {
        fit_width += char_width;
        start_char = i;
      } else {
        break;
      }
    }

    // Draw the fitting portion right-aligned
    if (start_char < strlen(text)) {
      const char* truncated_text = &text[start_char];
      int x_pos = x_start + width - fit_width;
      DrawText(canvas, *font, x_pos, y, *color, NULL, truncated_text);
    }
  }
}

static int usage(const char *progname) {
  fprintf(stderr, "usage: %s [options]\n", progname);
  fprintf(stderr, "Dual-line 3-section scrolling text for 32x160 LED matrix (5 panels of 32x32)\n");
  fprintf(stderr, "Layout: Left(32px) | Middle(96px scrolling) | Right(32px)\n");
  fprintf(stderr, "Version: 2.4.0 | Build: 2025-01-18 | Manual font selection + adjustable spacing\n");
  fprintf(stderr, "\nText Options:\n");
  fprintf(stderr, "\t-t1l <text>  : Line 1 Left section text (32px, left-aligned)\n");
  fprintf(stderr, "\t-t1m <text>  : Line 1 Middle section text (96px, scrolling)\n");
  fprintf(stderr, "\t-t1r <text>  : Line 1 Right section text (32px, right-aligned)\n");
  fprintf(stderr, "\t-t2l <text>  : Line 2 Left section text (32px, left-aligned)\n");
  fprintf(stderr, "\t-t2m <text>  : Line 2 Middle section text (96px, scrolling)\n");
  fprintf(stderr, "\t-t2r <text>  : Line 2 Right section text (32px, right-aligned)\n");
  fprintf(stderr, "\nColor Options:\n");
  fprintf(stderr, "\t-c1l <r,g,b> : Line 1 Left section color (default: 255,100,0 - orange)\n");
  fprintf(stderr, "\t-c1m <r,g,b> : Line 1 Middle section color (default: 0,255,0 - green)\n");
  fprintf(stderr, "\t-c1r <r,g,b> : Line 1 Right section color (default: 0,100,255 - blue)\n");
  fprintf(stderr, "\t-c2l <r,g,b> : Line 2 Left section color (default: 255,0,100 - magenta)\n");
  fprintf(stderr, "\t-c2m <r,g,b> : Line 2 Middle section color (default: 100,255,100 - light green)\n");
  fprintf(stderr, "\t-c2r <r,g,b> : Line 2 Right section color (default: 255,255,0 - yellow)\n");
  fprintf(stderr, "\nFont Options:\n");
  fprintf(stderr, "\t-flr <font>  : Font for left/right sections (default: 6x13.bdf)\n");
  fprintf(stderr, "\t-fm <font>   : Font for middle sections (default: 6x13.bdf)\n");
  fprintf(stderr, "\t-f <font>    : Font for all sections (overrides -flr and -fm)\n");
  fprintf(stderr, "\nDisplay Options:\n");
  fprintf(stderr, "\t-s <speed>   : Scroll speed in pixels per frame (default: 1)\n");
  fprintf(stderr, "\t-ls <pixels> : Line spacing between Line 1 and Line 2 (default: 4)\n");
  fprintf(stderr, "\t-fps <fps>   : Target FPS (default: 15, lower=less CPU)\n");
  fprintf(stderr, "\t--cpu-opt    : Enable CPU optimizations for Pi Zero\n");
  fprintf(stderr, "\nFont Recommendations:\n");
  fprintf(stderr, "\t• Left/Right (32px): 4x6, 5x7, 5x8, 6x9, 6x10, 6x12, 6x13\n");
  fprintf(stderr, "\t• Middle (96px): Any font (scrolling removes width constraints)\n");
  fprintf(stderr, "\t• 6x13.bdf: Optimal balance (fits 5+ chars in 32px sections)\n");
  fprintf(stderr, "\t• Adjustable spacing: 1-8px recommended for 32px height\n");
  fprintf(stderr, "\nDefaults optimized for: --led-row-addr-type=0 --led-multiplexing=1 --led-slowdown-gpio=2 --led-chain=5\n");
  fprintf(stderr, "\n");
  rgb_matrix::PrintMatrixFlags(stderr);
  return 1;
}

static bool parseColor(Color *c, const char *str) {
  return sscanf(str, "%hhu,%hhu,%hhu", &c->r, &c->g, &c->b) == 3;
}

// Helper function to parse color with error reporting
static bool parseColorWithError(Color *c, const char *str, const char *option_name) {
  if (parseColor(c, str)) {
    return true;
  } else {
    fprintf(stderr, "Invalid color spec for %s: %s (expected format: r,g,b)\n", option_name, str);
    return false;
  }
}

// Font optimization analysis for 32x160 display:
//
// Available fonts analyzed:
// - 4x6.bdf (4x6)   - Too small, poor readability
// - 5x7.bdf (5x7)   - Small but readable, fits 6+ chars in 32px
// - 5x8.bdf (5x8)   - Small, fits 6+ chars in 32px
// - 6x9.bdf (6x9)   - Good for small text, fits 5+ chars in 32px
// - 6x10.bdf (6x10) - Decent size, fits 5+ chars in 32px
// - 6x12.bdf (6x12) - Good readability, fits 5+ chars in 32px
// - 6x13.bdf (6x13) - Good size, fits 5+ chars in 32px
// - 7x13.bdf (7x13) - OPTIMAL: fits 4-5 chars in 32px, perfect 2-line fit
// - 7x14.bdf (7x14) - Slightly too tall for optimal 2-line spacing
// - 8x13.bdf (8x13) - Good but only fits 4 chars in 32px sections
// - 9x15.bdf (9x15) - Too large, only fits 3 chars in 32px sections
// - 9x18.bdf (9x18) - Too large for 32px height
// - 10x20.bdf (10x20) - Too large for both width and height
//
// Font configuration for 32x160 display optimization:
//
// CPU OPTIMIZATION NOTES:
// - Single font: ~5-10% less CPU usage (one font object, simpler rendering)
// - Multi-font: Better visual distinction but slightly more CPU overhead
// - Recommended: Use single font for CPU-constrained systems (Raspberry Pi Zero)
//
// Recommended fonts for different section types:
// - Left/Right sections (32px wide): 4x6, 5x7, 5x8, 6x9, 6x10, 6x12, 6x13
// - Middle sections (96px wide): Any font (no width limitation due to scrolling)
//
// Default configuration:
// - Left/Right: 6x13.bdf (fits 5+ characters in 32px)
// - Middle: 6x13.bdf (good readability for scrolling text)
// - Line spacing: 4px (adjustable via -ls option)
//
// Font size analysis:
// - 4x6: Very small, fits 8 chars in 32px
// - 5x7/5x8: Small, fits 6+ chars in 32px
// - 6x9/6x10/6x12/6x13: Medium, fits 5+ chars in 32px
// - 7x13/7x14: Large, fits 4+ chars in 32px
// - 8x13: Large, fits exactly 4 chars in 32px
// - 9x15+: Too large for optimal left/right section usage

int main(int argc, char *argv[]) {
  RGBMatrix::Options matrix_options;
  rgb_matrix::RuntimeOptions runtime_opt;

  // Set defaults for 32x160 LED matrix (32 rows, 160 cols = 5 panels of 32x32)
  // Using the specified hardware configuration
  matrix_options.hardware_mapping = "regular";
  matrix_options.rows = 32;
  matrix_options.cols = 32;  // Single panel width
  matrix_options.chain_length = 5;  // 5 panels chained = 160 total width
  matrix_options.parallel = 1;
  matrix_options.brightness = 80;
  matrix_options.pwm_bits = 11;
  matrix_options.pwm_lsb_nanoseconds = 130;
  matrix_options.led_rgb_sequence = "RGB";
  matrix_options.pixel_mapper_config = "";
  matrix_options.row_address_type = 0;  // As specified: --led-row-addr-type=0
  matrix_options.multiplexing = 1;      // As specified: --led-multiplexing=1
  matrix_options.scan_mode = 0;
  matrix_options.disable_hardware_pulsing = false;
  matrix_options.show_refresh_rate = false;
  matrix_options.inverse_colors = false;
  
  // Set runtime options for specified GPIO slowdown
  runtime_opt.gpio_slowdown = 2;  // As specified: --led-slowdown-gpio=2

  // Parse matrix options first
  if (!rgb_matrix::ParseOptionsFromFlags(&argc, &argv, &matrix_options, &runtime_opt)) {
    return usage(argv[0]);
  }

  // Application-specific options - 3-section layout
  const char* line1_left = "L1L";     // Line 1 Left section (32px)
  const char* line1_middle = "Line 1 Middle: Scrolling text demo";  // Line 1 Middle section (96px, scrolling)
  const char* line1_right = "L1R";    // Line 1 Right section (32px)
  const char* line2_left = "L2L";     // Line 2 Left section (32px)
  const char* line2_middle = "Line 2 Middle: Another scrolling message";  // Line 2 Middle section (96px, scrolling)
  const char* line2_right = "L2R";    // Line 2 Right section (32px)

  // Font configuration for different section types
  const char* font_left_right = "../fonts/6x13.bdf";  // Default for left/right sections
  const char* font_middle = "../fonts/6x13.bdf";      // Default for middle sections
  int line_spacing = 4;                                // Default line spacing in pixels
  bool manual_font_override = false;

  // Color options - different colors for each section for visual distinction
  Color color1_left(255, 100, 0);    // Line 1 Left: Orange
  Color color1_middle(0, 255, 0);    // Line 1 Middle: Green
  Color color1_right(0, 100, 255);   // Line 1 Right: Blue
  Color color2_left(255, 0, 100);    // Line 2 Left: Magenta
  Color color2_middle(100, 255, 100); // Line 2 Middle: Light Green
  Color color2_right(255, 255, 0);   // Line 2 Right: Yellow

  int scroll_speed = 1;
  int target_fps = 15;

  // CPU optimization flags
  bool cpu_optimize = false;  // Enable CPU optimizations for Pi Zero

  // Combined argument parsing for all options
  for (int i = 1; i < argc; i++) {
    if (strcmp(argv[i], "-t1l") == 0 && i + 1 < argc) {
      line1_left = argv[i + 1];
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-t1m") == 0 && i + 1 < argc) {
      line1_middle = argv[i + 1];
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-t1r") == 0 && i + 1 < argc) {
      line1_right = argv[i + 1];
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-t2l") == 0 && i + 1 < argc) {
      line2_left = argv[i + 1];
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-t2m") == 0 && i + 1 < argc) {
      line2_middle = argv[i + 1];
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-t2r") == 0 && i + 1 < argc) {
      line2_right = argv[i + 1];
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-c1l") == 0 && i + 1 < argc) {
      if (!parseColorWithError(&color1_left, argv[i + 1], "-c1l")) {
        return usage(argv[0]);
      }
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-c1m") == 0 && i + 1 < argc) {
      if (!parseColorWithError(&color1_middle, argv[i + 1], "-c1m")) {
        return usage(argv[0]);
      }
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-c1r") == 0 && i + 1 < argc) {
      if (!parseColorWithError(&color1_right, argv[i + 1], "-c1r")) {
        return usage(argv[0]);
      }
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-c2l") == 0 && i + 1 < argc) {
      if (!parseColorWithError(&color2_left, argv[i + 1], "-c2l")) {
        return usage(argv[0]);
      }
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-c2m") == 0 && i + 1 < argc) {
      if (!parseColorWithError(&color2_middle, argv[i + 1], "-c2m")) {
        return usage(argv[0]);
      }
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-c2r") == 0 && i + 1 < argc) {
      if (!parseColorWithError(&color2_right, argv[i + 1], "-c2r")) {
        return usage(argv[0]);
      }
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-s") == 0 && i + 1 < argc) {
      scroll_speed = atoi(argv[i + 1]);
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-ls") == 0 && i + 1 < argc) {
      line_spacing = atoi(argv[i + 1]);
      if (line_spacing < 1) line_spacing = 1;
      if (line_spacing > 10) line_spacing = 10;
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-flr") == 0 && i + 1 < argc) {
      font_left_right = argv[i + 1];
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-fm") == 0 && i + 1 < argc) {
      font_middle = argv[i + 1];
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-f") == 0 && i + 1 < argc) {
      // Override all fonts with single font
      font_left_right = argv[i + 1];
      font_middle = argv[i + 1];
      manual_font_override = true;
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-fps") == 0 && i + 1 < argc) {
      target_fps = atoi(argv[i + 1]);
      i++; // Skip next argument
    } else if (strcmp(argv[i], "--cpu-opt") == 0) {
      cpu_optimize = true;
      printf("🚀 CPU optimization enabled for Pi Zero\n");
    }
    // Note: LED matrix options (--led-*) are handled by ParseOptionsFromFlags above
  }

  // Set up signal handler for clean exit
  signal(SIGTERM, InterruptHandler);
  signal(SIGINT, InterruptHandler);

  // Create matrix
  RGBMatrix *matrix = RGBMatrix::CreateFromOptions(matrix_options, runtime_opt);
  if (matrix == NULL) {
    fprintf(stderr, "Failed to create matrix\n");
    return 1;
  }

  // Fast font selection - pre-optimized for 32x160 display
  if (!manual_font_override) {
    printf("⚡ Using optimized font for 32x160 display: 7x13.bdf\n");
    printf("� Font analysis: 7x13 fits 4-5 chars in 32px sections, 2 lines in 32px height\n");
  } else {
    printf("🔧 Using manual font override: %s\n", font_left_right);
  }

  // Load fonts for different section types
  Font font_lr;  // Font for left/right sections
  Font font_m;   // Font for middle sections

  // Load left/right sections font
  if (!font_lr.LoadFont(font_left_right)) {
    printf("⚠️  Failed to load left/right font %s, trying fallback...\n", font_left_right);
    if (!font_lr.LoadFont("../fonts/6x13.bdf")) {
      fprintf(stderr, "❌ Could not load left/right section font\n");
      return 1;
    } else {
      printf("📝 Using fallback for left/right: 6x13.bdf\n");
    }
  } else {
    printf("✅ Successfully loaded left/right font: %s\n", font_left_right);
  }

  // Load middle sections font
  if (!font_m.LoadFont(font_middle)) {
    printf("⚠️  Failed to load middle font %s, trying fallback...\n", font_middle);
    if (!font_m.LoadFont("../fonts/6x13.bdf")) {
      fprintf(stderr, "❌ Could not load middle section font\n");
      return 1;
    } else {
      printf("📝 Using fallback for middle: 6x13.bdf\n");
    }
  } else {
    printf("✅ Successfully loaded middle font: %s\n", font_middle);
  }

  // Font configuration summary
  if (!manual_font_override) {
    printf("📝 Font configuration: Left/Right=%s, Middle=%s\n", font_left_right, font_middle);
    printf("📐 Line spacing: %dpx between lines\n", line_spacing);
  } else {
    printf("🔧 Manual font override: %s for all sections\n", font_left_right);
    printf("📐 Line spacing: %dpx between lines\n", line_spacing);
  }

  // Calculate optimal line positions for 32-pixel height display
  int optimal_line1_y, optimal_line2_y;

  if (!manual_font_override) {
    // Calculate positions for 6x13 font with adjustable line spacing
    // 6x13 font: height=13, baseline=11
    optimal_line1_y = 11;  // Baseline position for 6x13 font
    optimal_line2_y = 11 + 13 + line_spacing;  // baseline + height + spacing
    printf("📐 Calculated positions for 6x13 font: Line1 Y=%d, Line2 Y=%d (spacing=%dpx)\n",
           optimal_line1_y, optimal_line2_y, line_spacing);
  } else {
    // Calculate positions for manual font with adjustable spacing
    optimal_line1_y = 12;
    optimal_line2_y = 12 + 13 + line_spacing;  // Conservative baseline + height + spacing
    printf("📐 Calculated positions for manual font: Line1 Y=%d, Line2 Y=%d (spacing=%dpx)\n",
           optimal_line1_y, optimal_line2_y, line_spacing);
  }

  printf("=== Dual-line 3-section scrolling text display ===\n");
  printf("Version: 2.4.0 | Build: 2025-01-18 | Manual font selection + adjustable spacing\n");
  printf("Display: 32x160 (32 rows, 5 panels of 32x32)\n");
  printf("Layout: Left(32px) | Middle(96px scrolling) | Right(32px)\n");
  printf("Hardware config: row-addr-type=0, multiplexing=1, gpio-slowdown=2, chain=5\n");
  printf("Fonts: Left/Right=%s, Middle=%s | Spacing: %dpx\n",
         font_left_right, font_middle, line_spacing);
  printf("Positions: Line1 Y=%d, Line2 Y=%d | LR Font: %dx%d | M Font: %dx%d\n",
         optimal_line1_y, optimal_line2_y,
         font_lr.CharacterWidth('M'), font_lr.height(),
         font_m.CharacterWidth('M'), font_m.height());
  printf("Line 1 - Left: '%s' | Middle: '%s' | Right: '%s'\n", line1_left, line1_middle, line1_right);
  printf("Line 2 - Left: '%s' | Middle: '%s' | Right: '%s'\n", line2_left, line2_middle, line2_right);
  printf("Colors - L1: (%d,%d,%d)|(%d,%d,%d)|(%d,%d,%d) L2: (%d,%d,%d)|(%d,%d,%d)|(%d,%d,%d)\n",
         color1_left.r, color1_left.g, color1_left.b,
         color1_middle.r, color1_middle.g, color1_middle.b,
         color1_right.r, color1_right.g, color1_right.b,
         color2_left.r, color2_left.g, color2_left.b,
         color2_middle.r, color2_middle.g, color2_middle.b,
         color2_right.r, color2_right.g, color2_right.b);
  printf("Scroll speed: %d pixels/frame\n", scroll_speed);
  printf("Target FPS: %d\n", target_fps);
  printf("Press CTRL-C to stop.\n");

  // Display loop setup
  FrameCanvas *offscreen = matrix->CreateFrameCanvas();

  // Initialize scroll data for middle sections only
  struct ScrollData scroll_data1_middle, scroll_data2_middle;
  InitScrollData(&scroll_data1_middle);
  InitScrollData(&scroll_data2_middle);

  // Prepare scroll data for middle sections using middle font
  PrepareScrollData(&scroll_data1_middle, line1_middle, &font_m);
  PrepareScrollData(&scroll_data2_middle, line2_middle, &font_m);

  // Scroll positions for middle sections
  int scroll_pos1_middle = 0;
  int scroll_pos2_middle = 0;

  // Display area configuration for 32x160 with 3-section layout
  const int display_width = matrix->width();   // Should be 160
  const int display_height = matrix->height(); // Should be 32

  // Section layout constants
  const int LEFT_WIDTH = 32;      // Left section width
  const int RIGHT_WIDTH = 32;     // Right section width
  const int MIDDLE_WIDTH = 96;    // Middle section width (160 - 32 - 32)
  const int LEFT_X = 0;           // Left section X position
  const int MIDDLE_X = 32;        // Middle section X position
  const int RIGHT_X = 128;        // Right section X position (160 - 32)

  // Line positioning - optimized based on selected font and display height
  const int line1_y = optimal_line1_y;  // Calculated optimal position for line 1
  const int line2_y = optimal_line2_y;  // Calculated optimal position for line 2

  // Frame timing with CPU optimization
  int frame_delay_usec = 1000000 / target_fps;

  // CPU optimization adjustments
  if (cpu_optimize) {
    printf("⚡ Applying CPU optimizations:\n");
    if (target_fps > 10) {
      target_fps = 10;
      frame_delay_usec = 1000000 / target_fps;
      printf("  📉 Reduced FPS to %d for lower CPU usage\n", target_fps);
    }
    if (scroll_speed > 1) {
      scroll_speed = 1;
      printf("  🐌 Reduced scroll speed to %d pixel/frame\n", scroll_speed);
    }
  }

  printf("Actual display area: %dx%d pixels\n", display_width, display_height);
  printf("Section layout: Left(0-%d) | Middle(%d-%d) | Right(%d-%d)\n",
         LEFT_WIDTH-1, MIDDLE_X, MIDDLE_X+MIDDLE_WIDTH-1, RIGHT_X, RIGHT_X+RIGHT_WIDTH-1);
  printf("Line 1 Y position: %d\n", line1_y);
  printf("Line 2 Y position: %d\n", line2_y);

  // Main display loop
  while (!interrupt_received) {
    offscreen->Clear();

    // === LINE 1 SECTIONS ===
    // IMPORTANT: Draw middle section first, then left/right sections on top
    // This prevents scrolling text from overwriting the static sections

    // Line 1 - Middle section (pixel-perfect scrolling) - DRAW FIRST
    if (scroll_data1_middle.valid) {
      if (scroll_data1_middle.total_width <= MIDDLE_WIDTH) {
        // Center the text if it fits within the 96px middle section
        DrawCenteredText(offscreen, &font_m, line1_middle, MIDDLE_X, line1_y, MIDDLE_WIDTH, &color1_middle);
      } else {
        // Smooth pixel-by-pixel scrolling for long text
        DrawScrollingText(offscreen, &font_m, &scroll_data1_middle, scroll_pos1_middle, MIDDLE_X, line1_y, MIDDLE_WIDTH, &color1_middle);
        // Increment by scroll_speed pixels per frame for smooth animation
        scroll_pos1_middle = (scroll_pos1_middle + scroll_speed) % scroll_data1_middle.total_width;
      }
    }

    // Line 1 - Left section (static, left-aligned) - DRAW ON TOP
    DrawLeftAlignedText(offscreen, &font_lr, line1_left, LEFT_X, line1_y, LEFT_WIDTH, &color1_left);

    // Line 1 - Right section (static, right-aligned) - DRAW ON TOP
    DrawRightAlignedText(offscreen, &font_lr, line1_right, RIGHT_X, line1_y, RIGHT_WIDTH, &color1_right);

    // === LINE 2 SECTIONS ===
    // IMPORTANT: Draw middle section first, then left/right sections on top
    // This prevents scrolling text from overwriting the static sections

    // Line 2 - Middle section (pixel-perfect scrolling) - DRAW FIRST
    if (scroll_data2_middle.valid) {
      if (scroll_data2_middle.total_width <= MIDDLE_WIDTH) {
        // Center the text if it fits within the 96px middle section
        DrawCenteredText(offscreen, &font_m, line2_middle, MIDDLE_X, line2_y, MIDDLE_WIDTH, &color2_middle);
      } else {
        // Smooth pixel-by-pixel scrolling for long text
        DrawScrollingText(offscreen, &font_m, &scroll_data2_middle, scroll_pos2_middle, MIDDLE_X, line2_y, MIDDLE_WIDTH, &color2_middle);
        // Increment by scroll_speed pixels per frame for smooth animation
        scroll_pos2_middle = (scroll_pos2_middle + scroll_speed) % scroll_data2_middle.total_width;
      }
    }

    // Line 2 - Left section (static, left-aligned) - DRAW ON TOP
    DrawLeftAlignedText(offscreen, &font_lr, line2_left, LEFT_X, line2_y, LEFT_WIDTH, &color2_left);

    // Line 2 - Right section (static, right-aligned) - DRAW ON TOP
    DrawRightAlignedText(offscreen, &font_lr, line2_right, RIGHT_X, line2_y, RIGHT_WIDTH, &color2_right);

    // Swap buffers for smooth animation
    offscreen = matrix->SwapOnVSync(offscreen);

    // Frame rate control
    usleep(frame_delay_usec);
  }

  // Cleanup and exit
  printf("\nShutting down...\n");
  delete matrix;

  return 0;
}
