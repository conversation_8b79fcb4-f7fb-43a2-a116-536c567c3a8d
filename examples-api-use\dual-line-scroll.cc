// -*- mode: c++; c-basic-offset: 2; indent-tabs-mode: nil; -*-
// Dual-line scrolling text display - C++ version (C-style implementation)
// Based on scrolling-text-example.cc reference and showremotetext-3part-working.cc
// Demonstrates scrolling text on exactly 2 lines using 8x13 font (or closest available)
// 
// This code is public domain
// (but note, that the led-matrix library this depends on is GPL v2)

#include "led-matrix.h"
#include "graphics.h"

#include <unistd.h>
#include <math.h>
#include <stdio.h>
#include <signal.h>
#include <string.h>
#include <stdlib.h>
#include <time.h>
#include <getopt.h>

using namespace rgb_matrix;

volatile bool interrupt_received = false;
static void InterruptHandler(int signo) {
  interrupt_received = true;
}

// Structure to hold scroll data for each line
struct ScrollData {
  char text[512];
  int total_width;
  int char_widths[512];
  int char_count;
  bool valid;
};

// Initialize scroll data structure
void InitScrollData(struct ScrollData* scroll_data) {
  memset(scroll_data, 0, sizeof(struct ScrollData));
  scroll_data->valid = false;
}

// Prepare scroll data - calculate character widths for smooth scrolling
void PrepareScrollData(struct ScrollData* scroll_data, const char* text, const Font* font) {
  if (!text || strlen(text) == 0) {
    scroll_data->valid = false;
    return;
  }
  
  // Copy text and add padding for smooth scrolling
  snprintf(scroll_data->text, sizeof(scroll_data->text), "%s   ", text);
  
  // Pre-calculate character widths for smooth pixel-perfect scrolling
  scroll_data->total_width = 0;
  scroll_data->char_count = strlen(scroll_data->text);
  
  for (int i = 0; i < scroll_data->char_count; i++) {
    scroll_data->char_widths[i] = font->CharacterWidth(scroll_data->text[i]);
    scroll_data->total_width += scroll_data->char_widths[i];
  }
  
  scroll_data->valid = true;
}

// Draw scrolling text with pixel-perfect character positioning
void DrawScrollingText(FrameCanvas* canvas, const Font* font, const struct ScrollData* scroll_data, 
                      int scroll_pos, int x_start, int y, int width, const Color* color) {
  if (!scroll_data->valid) return;
  
  // Calculate starting position in pixel space
  int pixel_x = -scroll_pos;
  int x_draw = x_start;
  
  // Draw only visible characters for performance
  for (int i = 0; i < scroll_data->char_count && pixel_x < width; i++) {
    int char_width = scroll_data->char_widths[i];
    
    // Only draw character if it's at least partially visible
    if (pixel_x + char_width > 0) {
      char single_char[2] = {scroll_data->text[i], '\0'};
      DrawText(canvas, *font, x_draw + pixel_x, y, *color, NULL, single_char);
    }
    pixel_x += char_width;
  }
}

// Draw centered text if it fits within the specified width
void DrawCenteredText(FrameCanvas* canvas, const Font* font, const char* text,
                     int x_start, int y, int width, const Color* color) {
  if (!text || strlen(text) == 0) return;
  
  // Calculate total text width
  int text_width = 0;
  for (int i = 0; text[i] != '\0'; i++) {
    text_width += font->CharacterWidth(text[i]);
  }
  
  // Center the text if it fits within the specified width
  if (text_width <= width) {
    int x_pos = x_start + (width - text_width) / 2;
    DrawText(canvas, *font, x_pos, y, *color, NULL, text);
  }
}

static int usage(const char *progname) {
  fprintf(stderr, "usage: %s [options]\n", progname);
  fprintf(stderr, "Options:\n");
  fprintf(stderr, "\t-t1 <text>   : Text for line 1 (default: \"Line 1: Scrolling text demo\")\n");
  fprintf(stderr, "\t-t2 <text>   : Text for line 2 (default: \"Line 2: Another scrolling message\")\n");
  fprintf(stderr, "\t-s <speed>   : Scroll speed in pixels per frame (default: 1)\n");
  fprintf(stderr, "\t-f <font>    : BDF font file (default: ../fonts/7x13.bdf - closest to 8x13)\n");
  fprintf(stderr, "\t-c <r,g,b>   : Text color (default: 0,255,0)\n");
  fprintf(stderr, "\t-fps <fps>   : Target FPS (default: 15)\n");
  fprintf(stderr, "\n");
  rgb_matrix::PrintMatrixFlags(stderr);
  return 1;
}

static bool parseColor(Color *c, const char *str) {
  return sscanf(str, "%hhu,%hhu,%hhu", &c->r, &c->g, &c->b) == 3;
}

int main(int argc, char *argv[]) {
  RGBMatrix::Options matrix_options;
  rgb_matrix::RuntimeOptions runtime_opt;

  // Set reasonable defaults for LED matrix
  matrix_options.hardware_mapping = "regular";
  matrix_options.rows = 32;
  matrix_options.cols = 64;
  matrix_options.chain_length = 1;
  matrix_options.parallel = 1;
  matrix_options.brightness = 80;
  matrix_options.pwm_bits = 11;
  matrix_options.pwm_lsb_nanoseconds = 130;
  matrix_options.led_rgb_sequence = "RGB";
  matrix_options.pixel_mapper_config = "";
  matrix_options.row_address_type = 0;
  matrix_options.multiplexing = 1;
  matrix_options.scan_mode = 0;
  matrix_options.disable_hardware_pulsing = false;
  matrix_options.show_refresh_rate = false;
  matrix_options.inverse_colors = false;

  // Parse matrix options first
  if (!rgb_matrix::ParseOptionsFromFlags(&argc, &argv, &matrix_options, &runtime_opt)) {
    return usage(argv[0]);
  }

  // Application-specific options
  const char* line1_text = "Line 1: Scrolling text demo";
  const char* line2_text = "Line 2: Another scrolling message";
  const char* font_file = "../fonts/7x13.bdf";  // Closest to 8x13 available
  Color text_color(0, 255, 0); // Green
  int scroll_speed = 1;
  int target_fps = 15;

  // Custom argument parsing for -t1 and -t2
  for (int i = 1; i < argc; i++) {
    if (strcmp(argv[i], "-t1") == 0 && i + 1 < argc) {
      line1_text = argv[i + 1];
      i++; // Skip next argument
    } else if (strcmp(argv[i], "-t2") == 0 && i + 1 < argc) {
      line2_text = argv[i + 1];
      i++; // Skip next argument
    }
  }

  int opt;
  while ((opt = getopt(argc, argv, "s:f:c:")) != -1) {
    switch (opt) {
      case 's': scroll_speed = atoi(optarg); break;
      case 'f': font_file = optarg; break;
      case 'c':
        if (!parseColor(&text_color, optarg)) {
          fprintf(stderr, "Invalid color spec: %s\n", optarg);
          return usage(argv[0]);
        }
        break;
      default: return usage(argv[0]);
    }
  }

  // Set up signal handler for clean exit
  signal(SIGTERM, InterruptHandler);
  signal(SIGINT, InterruptHandler);

  // Create matrix
  RGBMatrix *matrix = RGBMatrix::CreateFromOptions(matrix_options, runtime_opt);
  if (matrix == NULL) {
    fprintf(stderr, "Failed to create matrix\n");
    return 1;
  }

  // Load font - try 8x13 first, fallback to 7x13
  Font font;
  if (!font.LoadFont("../fonts/8x13.bdf")) {
    if (!font.LoadFont(font_file)) {
      fprintf(stderr, "Couldn't load font '%s' or 8x13.bdf\n", font_file);
      return 1;
    } else {
      printf("Using font: %s (8x13.bdf not found)\n", font_file);
    }
  } else {
    printf("Using 8x13.bdf font\n");
  }

  printf("Dual-line scrolling text display starting...\n");
  printf("Line 1: %s\n", line1_text);
  printf("Line 2: %s\n", line2_text);
  printf("Scroll speed: %d pixels/frame\n", scroll_speed);
  printf("Target FPS: %d\n", target_fps);
  printf("Press CTRL-C to stop.\n");

  // Display loop setup
  FrameCanvas *offscreen = matrix->CreateFrameCanvas();

  // Initialize scroll data for both lines
  struct ScrollData scroll_data1, scroll_data2;
  InitScrollData(&scroll_data1);
  InitScrollData(&scroll_data2);

  // Prepare scroll data for both lines
  PrepareScrollData(&scroll_data1, line1_text, &font);
  PrepareScrollData(&scroll_data2, line2_text, &font);

  // Scroll positions for both lines
  int scroll_pos1 = 0;
  int scroll_pos2 = 0;

  // Display area configuration
  const int display_width = matrix->width();
  const int display_height = matrix->height();

  // Line positioning - center the two lines vertically
  const int line1_y = display_height / 3;      // First line at 1/3 height
  const int line2_y = (display_height * 2) / 3; // Second line at 2/3 height

  // Frame timing
  const int frame_delay_usec = 1000000 / target_fps;

  printf("Display area: %dx%d pixels\n", display_width, display_height);
  printf("Line 1 Y position: %d\n", line1_y);
  printf("Line 2 Y position: %d\n", line2_y);

  // Main display loop
  while (!interrupt_received) {
    offscreen->Clear();

    // Draw line 1
    if (scroll_data1.valid) {
      if (scroll_data1.total_width <= display_width) {
        // Center the text if it fits
        DrawCenteredText(offscreen, &font, line1_text, 0, line1_y, display_width, &text_color);
      } else {
        // Scroll the text if it's too long
        DrawScrollingText(offscreen, &font, &scroll_data1, scroll_pos1, 0, line1_y, display_width, &text_color);
        scroll_pos1 = (scroll_pos1 + scroll_speed) % scroll_data1.total_width;
      }
    }

    // Draw line 2
    if (scroll_data2.valid) {
      if (scroll_data2.total_width <= display_width) {
        // Center the text if it fits
        DrawCenteredText(offscreen, &font, line2_text, 0, line2_y, display_width, &text_color);
      } else {
        // Scroll the text if it's too long
        DrawScrollingText(offscreen, &font, &scroll_data2, scroll_pos2, 0, line2_y, display_width, &text_color);
        scroll_pos2 = (scroll_pos2 + scroll_speed) % scroll_data2.total_width;
      }
    }

    // Swap buffers for smooth animation
    offscreen = matrix->SwapOnVSync(offscreen);

    // Frame rate control
    usleep(frame_delay_usec);
  }

  // Cleanup and exit
  printf("\nShutting down...\n");
  delete matrix;

  return 0;
}
