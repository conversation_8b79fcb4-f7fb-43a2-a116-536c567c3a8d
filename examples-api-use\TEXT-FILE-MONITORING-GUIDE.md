# Real-Time Text File Monitoring Guide

## 📝 **Text File Monitoring Overview**

The LED matrix display now supports real-time text file monitoring, allowing you to update display content without restarting the program. This is perfect for transit displays, status boards, and information displays that need frequent content updates.

## 🔧 **How It Works**

### **Monitoring Process:**
1. **File Check**: Program checks text file modification time every N seconds (configurable)
2. **Change Detection**: If file has been modified since last check, content is reloaded
3. **Content Update**: Display updates with new text content automatically
4. **Scroll Recalculation**: Middle section scroll data is recalculated for new text lengths
5. **Error Handling**: If file becomes unreadable, previous content is preserved

### **Priority System:**
1. **CLI Parameters** (highest priority - disables file monitoring)
2. **Text File Content** (used when no CLI text params provided)
3. **Built-in Defaults** (used when text file not found)

## 📁 **Text File Format**

### **File Structure (display.txt):**
```
L1L
Line 1 Middle: Real-time text file monitoring demo - edit this file to see updates
L1R
L2L
Line 2 Middle: Text updates automatically every 10 seconds without restart
L2R
```

### **Line Order (Exactly 6 lines):**
1. **Line 1 Left section text** (max 63 characters)
2. **Line 1 Middle section text** (max 255 characters)
3. **Line 1 Right section text** (max 63 characters)
4. **Line 2 Left section text** (max 63 characters)
5. **Line 2 Middle section text** (max 255 characters)
6. **Line 2 Right section text** (max 63 characters)

### **Format Rules:**
- **Exactly 6 lines** required (empty lines count)
- **UTF-8 encoding** recommended
- **No trailing spaces** (automatically trimmed)
- **Line breaks**: Unix (LF) or Windows (CRLF) both supported

## 🚀 **Usage Examples**

### **Basic Text File Monitoring:**
```bash
# Uses default display.txt file, checks every 10 seconds
sudo ./dual-line-scroll --led-row-addr-type=0 --led-chain=5

# Custom text file and check interval
sudo ./dual-line-scroll -tf status.txt -ti 5 --led-chain=5

# Alternative syntax for check interval
sudo ./dual-line-scroll -tf content.txt --text-interval 30 --led-chain=5
```

### **CLI Parameters Disable File Monitoring:**
```bash
# CLI text parameters take priority, file monitoring disabled
sudo ./dual-line-scroll -t1m "CLI Override Text" --led-chain=5

# Mixed: some CLI params disable ALL file monitoring
sudo ./dual-line-scroll -t1l "LEFT" -tf display.txt --led-chain=5
# Result: CLI params used, display.txt ignored
```

### **Real-Time Content Updates:**
```bash
# Start display with file monitoring
sudo ./dual-line-scroll -tf live-content.txt -ti 5 --led-chain=5

# In another terminal, update content
echo "BUS" > live-content.txt
echo "Route 42 to Downtown - Next: 5 min" >> live-content.txt
echo "42A" >> live-content.txt
echo "TRAIN" >> live-content.txt
echo "Blue Line to Airport - Next: 12 min" >> live-content.txt
echo "BLUE" >> live-content.txt

# Display updates automatically within 5 seconds
```

## ⚙️ **Configuration Options**

### **Command Line Parameters:**

| **Parameter** | **Description** | **Default** | **Range** | **Example** |
|---------------|-----------------|-------------|-----------|-------------|
| `-tf <file>` | Text file path | `display.txt` | Any valid path | `-tf /tmp/status.txt` |
| `-ti <seconds>` | Check interval | `10` | 1-300 seconds | `-ti 5` |
| `--text-interval <seconds>` | Alternative for -ti | `10` | 1-300 seconds | `--text-interval 30` |

### **Behavior Settings:**
- **Minimum interval**: 1 second (prevents excessive file I/O)
- **Maximum interval**: 300 seconds (5 minutes)
- **File size limit**: No explicit limit (reasonable text file sizes expected)
- **Character limits**: Left/Right 63 chars, Middle 255 chars

## 📊 **Monitoring Behavior**

### **Startup Messages:**
```
📁 No CLI text parameters provided, checking text file: display.txt
✅ Loaded text content from: display.txt
📝 Text file monitoring enabled (check interval: 10 seconds)
```

### **Update Messages:**
```
📝 Text file updated, reloading content...
✅ Text file reloaded successfully
🔄 Display refreshed with new text content
```

### **Error Messages:**
```
📝 Text file 'display.txt' not found, using built-in defaults
💡 Create 'display.txt' with 6 lines of text to enable file monitoring
⚠️  Failed to reload text file, keeping previous content
```

### **CLI Override Messages:**
```
🔧 Using CLI text parameters (text file monitoring disabled)
```

## 💡 **Best Practices**

### **For Production Systems:**
```bash
# Use longer intervals for production to reduce I/O
sudo ./dual-line-scroll -tf /var/lib/display/content.txt -ti 30 --led-chain=5

# Use absolute paths for reliability
sudo ./dual-line-scroll -tf /home/<USER>/display-content.txt -ti 15 --led-chain=5
```

### **For Development/Testing:**
```bash
# Use shorter intervals for quick testing
sudo ./dual-line-scroll -tf test-content.txt -ti 2 --led-chain=5

# Use relative paths for convenience
sudo ./dual-line-scroll -tf ./content/demo.txt -ti 5 --led-chain=5
```

### **Content Update Workflow:**
1. **Prepare new content** in a temporary file
2. **Validate format** (exactly 6 lines)
3. **Atomic update**: `mv temp-content.txt display.txt`
4. **Wait for update** (up to check interval seconds)
5. **Verify display** shows new content

## 🔄 **Real-World Use Cases**

### **Transit Display:**
```bash
# Bus/train arrival display
sudo ./dual-line-scroll -tf /var/transit/arrivals.txt -ti 15 --led-chain=5

# Content updated by transit API script:
# BUS
# Route 42 Downtown - Next: 3 min, 18 min
# 42
# RAIL  
# Blue Line Airport - Next: 7 min, 22 min
# BLUE
```

### **Status Board:**
```bash
# System status display
sudo ./dual-line-scroll -tf /var/status/system.txt -ti 20 --led-chain=5

# Content updated by monitoring script:
# SYS
# All systems operational - Uptime: 15 days
# OK
# NET
# Network: 98.5% - Last check: 14:32
# 98%
```

### **Information Display:**
```bash
# Event/announcement display
sudo ./dual-line-scroll -tf /var/info/announcements.txt -ti 60 --led-chain=5

# Content updated by CMS:
# INFO
# Welcome to TechConf 2024 - Registration: Hall A
# REG
# NEXT
# Keynote starts at 2:00 PM - Main Auditorium
# 2PM
```

## 🛠️ **Troubleshooting**

### **File Not Updating:**
- Check file permissions (readable by program user)
- Verify file modification time: `stat display.txt`
- Ensure check interval has passed
- Monitor console for update messages

### **Content Not Displaying Correctly:**
- Verify exactly 6 lines in file: `wc -l display.txt`
- Check for hidden characters: `cat -A display.txt`
- Ensure UTF-8 encoding: `file display.txt`
- Verify character limits (63 for left/right, 255 for middle)

### **Performance Issues:**
- Increase check interval for large files
- Use local files (avoid network mounts)
- Monitor system I/O with `iotop`

### **File Access Errors:**
```bash
# Check file permissions
ls -la display.txt

# Fix permissions if needed
chmod 644 display.txt

# Check if file exists and is readable
test -r display.txt && echo "File is readable" || echo "File not readable"
```

## 📈 **Performance Considerations**

### **Check Interval Impact:**
- **1-5 seconds**: High responsiveness, higher I/O load
- **10-30 seconds**: Balanced performance (recommended)
- **60+ seconds**: Low I/O load, slower updates

### **File Size Impact:**
- **Small files** (<1KB): Negligible impact
- **Large files** (>10KB): Consider longer intervals
- **Very large files** (>100KB): Not recommended

### **System Resources:**
- **CPU**: Minimal impact (file stat + read operations)
- **Memory**: Text content cached in memory
- **I/O**: One file stat + read operation per interval

The text file monitoring feature enables dynamic content updates perfect for real-world display applications!
