#!/bin/bash
# Build script for dual-line-scroll.cc
# C++ version of dual-line scrolling text display

set -e

echo "🚀 Building dual-line scrolling text display (C++ version)..."

# Check if we're in the right directory
if [ ! -f "dual-line-scroll.cc" ]; then
    echo "❌ Error: dual-line-scroll.cc not found!"
    echo "Please run this script from the examples-api-use directory"
    exit 1
fi

# Build the main library first
echo "🔧 Building RGB matrix library..."
make -C .. -j$(nproc)

# Build our program
echo "🔧 Building dual-line-scroll..."
make -f Makefile.dual-line-scroll clean
make -f Makefile.dual-line-scroll -j$(nproc)

if [ -f "dual-line-scroll" ]; then
    echo ""
    echo "✅ Build successful!"
    echo ""
    echo "📊 This C program demonstrates:"
    echo "  ✅ Dual-line scrolling text display"
    echo "  ✅ 8x13 font support (with 7x13 fallback)"
    echo "  ✅ Smooth pixel-perfect scrolling"
    echo "  ✅ Customizable text for each line"
    echo "  ✅ Configurable scroll speed and colors"
    echo "  ✅ Proper LED matrix initialization"
    echo "  ✅ Clean exit handling (CTRL-C)"
    echo ""
    echo "🚀 Usage examples:"
    echo ""
    echo "  # Basic usage with default text:"
    echo "  sudo ./dual-line-scroll"
    echo ""
    echo "  # Custom text for both lines:"
    echo "  sudo ./dual-line-scroll -t1 \"Welcome to LED Matrix\" -t2 \"Dual line scrolling demo\""
    echo ""
    echo "  # Custom colors and speed:"
    echo "  sudo ./dual-line-scroll -c 255,0,0 -s 2 -t1 \"Red text\" -t2 \"Faster scrolling\""
    echo ""
    echo "  # With LED matrix configuration:"
    echo "  sudo ./dual-line-scroll --led-rows=32 --led-cols=64 --led-brightness=50 \\"
    echo "    -t1 \"Line 1 text\" -t2 \"Line 2 text\""
    echo ""
    echo "  # Using different font:"
    echo "  sudo ./dual-line-scroll -f ../fonts/6x13.bdf -t1 \"Custom font\" -t2 \"Different size\""
    echo ""
    echo "📝 Command line options:"
    echo "  -t1 <text>   : Text for line 1"
    echo "  -t2 <text>   : Text for line 2"
    echo "  -s <speed>   : Scroll speed (pixels per frame)"
    echo "  -f <font>    : BDF font file path"
    echo "  -c <r,g,b>   : Text color (RGB values)"
    echo "  -fps <fps>   : Target frame rate"
    echo "  --led-*      : Standard LED matrix options"
    echo ""
    echo "🎯 Features:"
    echo "  🔤 Uses 8x13 font (or closest available)"
    echo "  📏 Automatic text centering for short text"
    echo "  🔄 Smooth scrolling for long text"
    echo "  ⚡ Optimized character-by-character rendering"
    echo "  🎨 Customizable colors and speed"
    echo "  🖥️  Proper dual-line positioning"
    echo ""
else
    echo "❌ Build failed!"
    exit 1
fi
