#!/bin/bash
# Build script for dual-line-scroll.cc
# C++ version of dual-line scrolling text display

set -e

echo "🚀 Building dual-line 3-section scrolling text display with manual font selection (C++ version)..."

# Check if we're in the right directory
if [ ! -f "dual-line-scroll.cc" ]; then
    echo "❌ Error: dual-line-scroll.cc not found!"
    echo "Please run this script from the examples-api-use directory"
    exit 1
fi

# Build the main library first
echo "🔧 Building RGB matrix library..."
make -C .. -j$(nproc)

# Build our program
echo "🔧 Building dual-line-scroll..."
make -f Makefile.dual-line-scroll clean
make -f Makefile.dual-line-scroll -j$(nproc)

if [ -f "dual-line-scroll" ]; then
    echo ""
    echo "✅ Build successful!"
    echo ""
    echo "📊 This C++ program demonstrates:"
    echo "  ✅ Dual-line 3-section scrolling text display"
    echo "  ✅ Layout: Left(32px) | Middle(96px scrolling) | Right(32px)"
    echo "  ✅ 🆕 Manual font selection for left/right and middle sections"
    echo "  ✅ 🆕 Adjustable line spacing (1-10px) for optimal readability"
    echo "  ✅ 🆕 6x13.bdf default (fits 5+ chars in 32px sections)"
    echo "  ✅ 🆕 Clean, optimized code with consistent documentation"
    echo "  ✅ 🆕 Real-time text file monitoring (display.txt)"
    echo "  ✅ 🆕 Configurable check interval (1-300 seconds)"
    echo "  ✅ 🆕 Automatic content updates without restart"
    echo "  ✅ Smooth pixel-by-pixel scrolling with pixel-perfect clipping"
    echo "  ✅ Static left-aligned and right-aligned text in side sections"
    echo "  ✅ 6 customizable text parameters (3 per line)"
    echo "  ✅ 6 individual color settings (different color per section)"
    echo "  ✅ Attractive default colors: Orange|Green|Blue and Magenta|Light Green|Yellow"
    echo "  ✅ Optimized for 32x160 LED matrix (5 panels of 32x32)"
    echo "  ✅ Hardware defaults: row-addr-type=0, multiplexing=1, gpio-slowdown=2, chain=5"
    echo "  ✅ Clean exit handling (CTRL-C)"
    echo ""
    echo "🚀 Usage examples:"
    echo ""
    echo "  # Basic usage with default text:"
    echo "  sudo ./dual-line-scroll"
    echo ""
    echo "  # Custom text for all 6 sections:"
    echo "  sudo ./dual-line-scroll -t1l \"LEFT1\" -t1m \"Middle scrolling text for line 1\" -t1r \"RIGHT1\" \\"
    echo "    -t2l \"LEFT2\" -t2m \"Middle scrolling text for line 2\" -t2r \"RIGHT2\""
    echo ""
    echo "  # Custom fonts and spacing:"
    echo "  sudo ./dual-line-scroll -flr ../fonts/8x13.bdf -fm ../fonts/6x13.bdf -ls 6 \\"
    echo "    -t1m \"Large left/right fonts\" -t2m \"Different middle font with more spacing\""
    echo ""
    echo "  # CPU optimized for Pi Zero:"
    echo "  sudo ./dual-line-scroll --cpu-opt -f ../fonts/6x13.bdf -fps 8 -s 1 \\"
    echo "    --led-gpio-slowdown=4 --led-pwm-bits=8 --led-chain=5"
    echo ""
    echo "  # Real-time text file monitoring:"
    echo "  sudo ./dual-line-scroll --led-chain=5 -tf display.txt -ti 5"
    echo "  # Edit display.txt file to see content update every 5 seconds"
    echo ""
    echo "  # With LED matrix configuration (32x160 display):"
    echo "  sudo ./dual-line-scroll --led-row-addr-type=0 --led-multiplexing=1 --led-slowdown-gpio=2 --led-chain=5 \\"
    echo "    -t1l \"L1\" -t1m \"Line 1 scrolling\" -t1r \"R1\" -t2l \"L2\" -t2m \"Line 2 scrolling\" -t2r \"R2\""
    echo ""
    echo "  # Using different font:"
    echo "  sudo ./dual-line-scroll -f ../fonts/6x13.bdf -t1 \"Custom font\" -t2 \"Different size\""
    echo ""
    echo "📝 Command line options:"
    echo "  Text Options:"
    echo "    -t1l <text>  : Line 1 Left section (32px, left-aligned)"
    echo "    -t1m <text>  : Line 1 Middle section (96px, scrolling)"
    echo "    -t1r <text>  : Line 1 Right section (32px, right-aligned)"
    echo "    -t2l <text>  : Line 2 Left section (32px, left-aligned)"
    echo "    -t2m <text>  : Line 2 Middle section (96px, scrolling)"
    echo "    -t2r <text>  : Line 2 Right section (32px, right-aligned)"
    echo "  Color Options:"
    echo "    -c1l <r,g,b> : Line 1 Left section color (default: orange)"
    echo "    -c1m <r,g,b> : Line 1 Middle section color (default: green)"
    echo "    -c1r <r,g,b> : Line 1 Right section color (default: blue)"
    echo "    -c2l <r,g,b> : Line 2 Left section color (default: magenta)"
    echo "    -c2m <r,g,b> : Line 2 Middle section color (default: light green)"
    echo "    -c2r <r,g,b> : Line 2 Right section color (default: yellow)"
    echo "  Font Options:"
    echo "    -flr <font>  : Font for left/right sections (default: 6x13.bdf)"
    echo "    -fm <font>   : Font for middle sections (default: 6x13.bdf)"
    echo "    -f <font>    : Font for all sections (overrides -flr and -fm)"
    echo "  Display Options:"
    echo "    -s <speed>   : Scroll speed (pixels per frame)"
    echo "    -ls <pixels> : Line spacing between lines (1-10px, default: 4)"
    echo "    -fps <fps>   : Target frame rate (lower=less CPU)"
    echo "    --cpu-opt    : Enable CPU optimizations for Pi Zero"
    echo "  Text File Monitoring:"
    echo "    -tf <file>   : Text file to monitor (default: display.txt)"
    echo "    -ti <seconds>: Check interval (1-300s, default: 10)"
    echo "    --text-interval <seconds> : Alternative for -ti"
    echo "    --led-*      : Standard LED matrix options"
    echo ""
    echo "🎯 Features:"
    echo "  🔤 Manual font selection: different fonts for left/right vs middle sections"
    echo "  📐 3-section layout: Left(32px) | Middle(96px) | Right(32px)"
    echo "  📏 Left sections: left-aligned static text"
    echo "  🔄 Middle sections: smooth pixel-by-pixel scrolling with pixel-perfect clipping"
    echo "  📐 Right sections: right-aligned static text"
    echo "  🎨 6 individual colors (one per section)"
    echo "  📏 Adjustable line spacing (1-10px) for optimal readability"
    echo "  🖥️  Optimized for 32x160 display (5 panels of 32x32)"
    echo "  ⚙️  Hardware-specific defaults for reliable operation"
    echo ""
else
    echo "❌ Build failed!"
    exit 1
fi
